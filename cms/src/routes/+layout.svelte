<script lang="ts">
	import '../app.css';

	import { setNavigationStore } from '$lib/stores/navigation.svelte.js';
	import { setAuthStore } from '$lib/stores/auth.svelte.js';
	import HomeIcon from '@tabler/icons-svelte/icons/home';
	import ListDetailsIcon from '@tabler/icons-svelte/icons/list-details';
	import PlusIcon from '@tabler/icons-svelte/icons/plus';

	let { children } = $props();

	// Initialize auth store first
	const authStore = setAuthStore();

	// Initialize navigation store without hardcoded user
	const navigationStore = setNavigationStore({
		headerTitle: 'Dashboard',
		currentRoute: '/dashboard',
		items: [
			{
				id: 'reviews',
				title: 'Reviews',
				url: '/reviews',
				icon: ListDetailsIcon,
				badge: 3,
				permissions: ['ADMIN']
			}
		]
	});
</script>

{@render children()}
