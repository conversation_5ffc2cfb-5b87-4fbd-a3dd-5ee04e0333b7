<script lang="ts">
	import '../app.css';

	import { setNavigationStore } from '$lib/stores/navigation.svelte.js';
	import HomeIcon from '@tabler/icons-svelte/icons/home';
	import ListDetailsIcon from '@tabler/icons-svelte/icons/list-details';
	import PlusIcon from '@tabler/icons-svelte/icons/plus';
	import SettingsIcon from '@tabler/icons-svelte/icons/settings';

	let { children } = $props();

	setNavigationStore({
		headerTitle: 'Dashboard',
		currentRoute: '/dashboard',
		items: [
			{
				id: 'dashboard',
				title: 'Dashboard',
				url: '/dashboard',
				icon: HomeIcon
			},
			{
				id: 'reviews',
				title: 'Reviews',
				url: '/reviews',
				icon: ListDetailsIcon,
				badge: 3
			},
			{
				id: 'settings',
				title: 'Settings',
				url: '/settings',
				icon: SettingsIcon
			}
		],
		user: {
			name: 'shadcn',
			email: '<EMAIL>'
		},
		headerActions: [
			{
				id: 'add-new',
				title: 'Add New',
				icon: PlusIcon,
				onClick: () => {
					console.log('Add new clicked');
				}
			}
		],
		breadcrumbs: [{ title: 'Home', url: '/' }, { title: 'Dashboard' }]
	});
</script>

{@render children()}
