<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import { goto } from '$app/navigation';
	import { getAuthStore } from '$lib/stores/auth.svelte.js';
	import ShieldXIcon from '@tabler/icons-svelte/icons/shield-x';

	const authStore = getAuthStore();

	function goBack() {
		goto('/reviews');
	}

	function logout() {
		authStore.logout();
	}
</script>

<div class="flex min-h-svh items-center justify-center bg-muted/50">
	<div class="mx-auto max-w-md text-center">
		<div class="mb-6">
			<ShieldXIcon class="mx-auto h-16 w-16 text-destructive" />
		</div>
		
		<h1 class="text-2xl font-bold tracking-tight mb-2">Access Denied</h1>
		<p class="text-muted-foreground mb-6">
			You don't have permission to access this resource. Please contact your administrator if you believe this is an error.
		</p>
		
		<div class="flex flex-col gap-3 sm:flex-row sm:justify-center">
			<Button onclick={goBack} variant="default">
				Go Back
			</Button>
			<Button onclick={logout} variant="outline">
				Logout
			</Button>
		</div>
		
		{#if authStore.user}
			<div class="mt-6 text-sm text-muted-foreground">
				Logged in as: {authStore.user.email}
			</div>
		{/if}
	</div>
</div>
