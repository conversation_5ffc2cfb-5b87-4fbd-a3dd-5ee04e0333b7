<script lang="ts">
	import AppSidebar from '$lib/components/app-sidebar.svelte';
	import DataTable from '$lib/components/data-table.svelte';
	import SectionCards from '$lib/components/section-cards.svelte';
	import SiteHeader from '$lib/components/site-header.svelte';
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';
	import { setNavigationStore } from '$lib/stores/navigation.svelte.js';
	import { getAuthStore } from '$lib/stores/auth.svelte.js';
	import { useAuthGuard } from '$lib/hooks/auth-guard.svelte.js';
	import ListDetailsIcon from '@tabler/icons-svelte/icons/list-details';
	import HomeIcon from '@tabler/icons-svelte/icons/home';
	import SettingsIcon from '@tabler/icons-svelte/icons/settings';
	import data from './data.js';

	// Auth guard - protect this route
	const authGuard = useAuthGuard();
	const authStore = getAuthStore();

	// Initialize navigation store with auth-aware data
	const navigationStore = setNavigationStore({
		headerTitle: 'Dashboard',
		currentRoute: '/dashboard',
		items: [
			{
				id: 'dashboard',
				title: 'Dashboard',
				url: '/dashboard',
				icon: HomeIcon
			},
			{
				id: 'reviews',
				title: 'Reviews',
				url: '/reviews',
				icon: ListDetailsIcon,
				badge: data.length
			},
			{
				id: 'settings',
				title: 'Settings',
				url: '/settings',
				icon: SettingsIcon
			}
		],
		user: authStore.user ? {
			name: authStore.user.name,
			email: authStore.user.email,
			avatar: authStore.user.avatar
		} : undefined,
		breadcrumbs: [
			{ title: 'Home', url: '/' },
			{ title: 'Dashboard' }
		]
	});
</script>

{#if authGuard.isLoading}
	<div class="flex min-h-svh items-center justify-center">
		<div class="text-center">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
			<p class="text-muted-foreground">Loading...</p>
		</div>
	</div>
{:else if authGuard.isAuthenticated}
	<Sidebar.Provider
		style="--sidebar-width: calc(var(--spacing) * 72); --header-height: calc(var(--spacing) * 12);"
	>
		<AppSidebar variant="inset" />
		<Sidebar.Inset>
			<SiteHeader />
			<div class="flex flex-1 flex-col">
				<div class="@container/main flex flex-1 flex-col gap-2">
					<div class="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
						<SectionCards />

						<DataTable {data} />
					</div>
				</div>
			</div>
		</Sidebar.Inset>
	</Sidebar.Provider>
{/if}
