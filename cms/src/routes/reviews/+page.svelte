<script lang="ts">
	import AppSidebar from '$lib/components/app-sidebar.svelte';
	import DataTable from '$lib/components/data-table.svelte';
	import SectionCards from '$lib/components/section-cards.svelte';
	import SiteHeader from '$lib/components/site-header.svelte';
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';
	import { useAuthGuard } from '$lib/hooks/auth-guard.svelte.js';
	import { getNavigationStore } from '$lib/stores/navigation.svelte.js';
	import { UserRole } from '$lib/types/auth';
	import { page } from '$app/stores';
	import data from './data.js';

	// Auth guard - protect this route
	const authGuard = useAuthGuard({ requiredRoles: [UserRole.ADMIN] });

	// Get navigation store and update it for this page
	const navigationStore = getNavigationStore();

	// Update navigation for reviews page
	navigationStore.setHeaderTitle('Reviews');
	navigationStore.setCurrentRoute('/reviews');
	navigationStore.setBreadcrumbs([{ title: 'Home', url: '/' }, { title: 'Reviews' }]);
</script>

{#if authGuard.isLoading}
	<div class="flex min-h-svh items-center justify-center">
		<div class="text-center">
			<div class="border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2"></div>
			<p class="text-muted-foreground">Loading...</p>
		</div>
	</div>
{:else if authGuard.isAuthenticated}
	<Sidebar.Provider
		style="--sidebar-width: calc(var(--spacing) * 72); --header-height: calc(var(--spacing) * 12);"
	>
		<AppSidebar variant="inset" />
		<Sidebar.Inset>
			<SiteHeader />
			<div class="flex flex-1 flex-col">
				<div class="@container/main flex flex-1 flex-col gap-2">
					<div class="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
						<DataTable {data} page={$page} />
					</div>
				</div>
			</div>
		</Sidebar.Inset>
	</Sidebar.Provider>
{/if}
