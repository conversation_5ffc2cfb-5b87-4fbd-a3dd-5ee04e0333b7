import { goto } from '$app/navigation';
import { getAuthStore } from '$lib/stores/auth.svelte.js';

/**
 * Auth guard hook that protects routes from unauthorized access
 * Usage: Call this in components that require authentication
 */
export function useAuthGuard(
	options: {
		redirectTo?: string;
		requiredPermissions?: string[];
		requiredRole?: string;
	} = {}
) {
	const authStore = getAuthStore();
	const { redirectTo = '/login', requiredPermissions = [], requiredRole } = options;

	// Check authentication and permissions
	$effect(() => {
		// Wait for auth to finish loading
		if (authStore.isLoading) return;

		// Check if user is authenticated
		if (!authStore.isAuthenticated) {
			goto(redirectTo);
			return;
		}

		// Check required role
		if (requiredRole && !authStore.hasRole(requiredRole)) {
			goto('/unauthorized');
			return;
		}

		// Check required permissions
		if (requiredPermissions.length > 0) {
			const hasAllPermissions = requiredPermissions.every((permission) =>
				authStore.hasPermission(permission)
			);

			if (!hasAllPermissions) {
				goto('/unauthorized');
				return;
			}
		}
	});

	return {
		isAuthenticated: authStore.isAuthenticated,
		isLoading: authStore.isLoading,
		user: authStore.user
	};
}

/**
 * Redirect authenticated users away from auth pages (like login)
 * Usage: Call this in login/register pages
 */
export function useGuestGuard(redirectTo: string = '/reviews') {
	const authStore = getAuthStore();

	$effect(() => {
		if (!authStore.isLoading && authStore.isAuthenticated) {
			goto(redirectTo);
		}
	});

	return {
		isAuthenticated: authStore.isAuthenticated,
		isLoading: authStore.isLoading
	};
}

/**
 * Permission-based guard for specific features
 */
export function usePermissionGuard(permission: string) {
	const authStore = getAuthStore();
	const hasPermission = $derived(authStore.hasPermission(permission));

	return {
		get hasPermission() { return hasPermission; },
		get isAuthenticated() { return authStore.isAuthenticated; },
		get isLoading() { return authStore.isLoading; }
	};
}

/**
 * Role-based guard for specific features
 */
export function useRoleGuard(role: string) {
	const authStore = getAuthStore();
	const hasRole = $derived(authStore.hasRole(role));

	return {
		get hasRole() { return hasRole; },
		get isAuthenticated() { return authStore.isAuthenticated; },
		get isLoading() { return authStore.isLoading; }
	};
}
