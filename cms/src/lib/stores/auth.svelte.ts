import { getContext, setContext } from 'svelte';
import { goto } from '$app/navigation';
import { browser } from '$app/environment';

// Types for authentication system
export interface User {
	id: string;
	name: string;
	email: string;
	avatar?: string;
	role?: string;
}

export interface AuthState {
	user: User | null;
	isAuthenticated: boolean;
	isLoading: boolean;
	error: string | null;
}

// Authentication Store Class
class AuthStore {
	// Core state using Svelte 5 runes
	private _user = $state<User | null>(null);
	private _isLoading = $state<boolean>(true);
	private _error = $state<string | null>(null);

	// Computed values
	user = $derived(this._user);
	isAuthenticated = $derived(this._user !== null);
	isLoading = $derived(this._isLoading);
	error = $derived(this._error);

	constructor() {
		// Initialize auth state from localStorage on client side
		if (browser) {
			this.initializeAuth();
		}
	}

	// Initialize authentication state from stored data
	private initializeAuth(): void {
		try {
			const storedUser = localStorage.getItem('auth_user');
			const storedToken = localStorage.getItem('auth_token');

			if (storedUser && storedToken) {
				this._user = JSON.parse(storedUser);
			}
		} catch (error) {
			console.error('Failed to initialize auth:', error);
			this.clearAuth();
		} finally {
			this._isLoading = false;
		}
	}

	// Login method
	async login(email: string, password: string): Promise<boolean> {
		this._isLoading = true;
		this._error = null;

		try {
			// Simulate API call - replace with your actual authentication logic
			await new Promise((resolve) => setTimeout(resolve, 1000));

			// Mock authentication - replace with real API call
			if (email === '<EMAIL>' && password === 'password') {
				const user: User = {
					id: '1',
					name: 'Admin User',
					email: email,
					avatar: '/avatars/admin.jpg',
					role: 'admin'
				};

				this._user = user;

				// Store auth data
				if (browser) {
					localStorage.setItem('auth_user', JSON.stringify(user));
					localStorage.setItem('auth_token', 'mock_token_123');
				}

				return true;
			} else {
				this._error = 'Invalid email or password';
				return false;
			}
		} catch (error) {
			this._error = error instanceof Error ? error.message : 'Login failed';
			return false;
		} finally {
			this._isLoading = false;
		}
	}

	// Logout method
	logout(): void {
		this._user = null;
		this._error = null;

		// Clear stored auth data
		if (browser) {
			localStorage.removeItem('auth_user');
			localStorage.removeItem('auth_token');
		}

		// Redirect to login
		goto('/login');
	}

	// Clear authentication data
	private clearAuth(): void {
		this._user = null;
		this._error = null;

		if (browser) {
			localStorage.removeItem('auth_user');
			localStorage.removeItem('auth_token');
		}
	}

	// Set error message
	setError(error: string | null): void {
		this._error = error;
	}

	// Clear error message
	clearError(): void {
		this._error = null;
	}

	// Check if user has specific role
	hasRole(role: string): boolean {
		return this._user?.role === role;
	}

	// Check if user has any of the specified roles
	hasAnyRole(roles: string[]): boolean {
		if (!this._user?.role) return false;
		return roles.includes(this._user.role);
	}

	// Update user information
	updateUser(updates: Partial<User>): void {
		if (this._user) {
			this._user = { ...this._user, ...updates };

			// Update stored user data
			if (browser) {
				localStorage.setItem('auth_user', JSON.stringify(this._user));
			}
		}
	}

	// Refresh authentication (e.g., validate token with server)
	async refresh(): Promise<boolean> {
		if (!browser) return false;

		const token = localStorage.getItem('auth_token');
		if (!token) {
			this.clearAuth();
			return false;
		}

		try {
			this._isLoading = true;

			// Simulate token validation - replace with actual API call
			await new Promise((resolve) => setTimeout(resolve, 500));

			// Mock validation - in real app, validate token with server
			if (token === 'mock_token_123') {
				return true;
			} else {
				this.clearAuth();
				return false;
			}
		} catch (error) {
			console.error('Token refresh failed:', error);
			this.clearAuth();
			return false;
		} finally {
			this._isLoading = false;
		}
	}
}

// Context management
const AUTH_CONTEXT_KEY = Symbol('auth');

export function setAuthStore(): AuthStore {
	const store = new AuthStore();
	setContext(AUTH_CONTEXT_KEY, store);
	return store;
}

export function getAuthStore(): AuthStore {
	const store = getContext<AuthStore>(AUTH_CONTEXT_KEY);
	if (!store) {
		throw new Error(
			'Auth store not found. Make sure to call setAuthStore() in a parent component.'
		);
	}
	return store;
}

// Export the store class for direct usage
export { AuthStore };

// Helper function to create auth store without context
export function createAuthStore(): AuthStore {
	return new AuthStore();
}
