import type { Icon } from '@tabler/icons-svelte';
import { getContext, setContext } from 'svelte';

// Types for navigation system
export interface NavigationItem {
	id: string;
	title: string;
	url: string;
	icon?: Icon;
	badge?: string | number;
	children?: NavigationItem[];
	isVisible?: boolean;
	permissions?: string[];
	metadata?: Record<string, any>;
}

export interface BreadcrumbItem {
	title: string;
	url?: string;
}

export interface HeaderAction {
	id: string;
	title: string;
	icon?: Icon;
	onClick: () => void;
	isVisible?: boolean;
}

export interface NavigationState {
	items: NavigationItem[];
	headerTitle: string;
	breadcrumbs: BreadcrumbItem[];
	headerActions: HeaderAction[];
	currentRoute: string;
	user?: {
		name: string;
		email: string;
		avatar?: string;
	};
}

// Navigation Store Class
class NavigationStore {
	// Core state using Svelte 5 runes
	private _items = $state<NavigationItem[]>([]);
	private _headerTitle = $state<string>('Dashboard');
	private _breadcrumbs = $state<BreadcrumbItem[]>([]);
	private _headerActions = $state<HeaderAction[]>([]);
	private _currentRoute = $state<string>('/');
	private _user = $state<NavigationState['user']>();

	// Computed values
	items = $derived(this._items);
	headerTitle = $derived(this._headerTitle);
	breadcrumbs = $derived(this._breadcrumbs);
	headerActions = $derived(this._headerActions);
	currentRoute = $derived(this._currentRoute);
	user = $derived(this._user);

	// Get visible navigation items
	visibleItems = $derived(this._items.filter((item) => item.isVisible !== false));

	// Get active navigation item based on current route
	activeItem = $derived(this.findActiveItem(this._items, this._currentRoute));

	// Get flattened navigation items (including children)
	flatItems = $derived(this.flattenItems(this._items));

	constructor(initialState?: Partial<NavigationState>) {
		if (initialState) {
			this._items = initialState.items || [];
			this._headerTitle = initialState.headerTitle || 'Dashboard';
			this._breadcrumbs = initialState.breadcrumbs || [];
			this._headerActions = initialState.headerActions || [];
			this._currentRoute = initialState.currentRoute || '/';
			this._user = initialState.user;
		}
	}

	// Navigation Items Management
	setItems(items: NavigationItem[]): void {
		this._items = items;
	}

	addItem(item: NavigationItem, parentId?: string): void {
		if (parentId) {
			this.addChildItem(parentId, item);
		} else {
			this._items = [...this._items, item];
		}
	}

	removeItem(id: string): void {
		this._items = this.removeItemRecursive(this._items, id);
	}

	updateItem(id: string, updates: Partial<NavigationItem>): void {
		this._items = this.updateItemRecursive(this._items, id, updates);
	}

	getItem(id: string): NavigationItem | undefined {
		return this.findItemRecursive(this._items, id);
	}

	// Header Management
	setHeaderTitle(title: string): void {
		this._headerTitle = title;
	}

	setBreadcrumbs(breadcrumbs: BreadcrumbItem[]): void {
		this._breadcrumbs = breadcrumbs;
	}

	addBreadcrumb(breadcrumb: BreadcrumbItem): void {
		this._breadcrumbs = [...this._breadcrumbs, breadcrumb];
	}

	clearBreadcrumbs(): void {
		this._breadcrumbs = [];
	}

	// Header Actions Management
	setHeaderActions(actions: HeaderAction[]): void {
		this._headerActions = actions;
	}

	addHeaderAction(action: HeaderAction): void {
		this._headerActions = [...this._headerActions, action];
	}

	removeHeaderAction(id: string): void {
		this._headerActions = this._headerActions.filter((action) => action.id !== id);
	}

	// Route Management
	setCurrentRoute(route: string): void {
		this._currentRoute = route;
		this.updateBreadcrumbsFromRoute(route);
	}

	// User Management
	setUser(user: NavigationState['user']): void {
		this._user = user;
	}

	// Utility Methods
	private findActiveItem(items: NavigationItem[], route: string): NavigationItem | undefined {
		for (const item of items) {
			if (item.url === route) {
				return item;
			}
			if (item.children) {
				const childMatch = this.findActiveItem(item.children, route);
				if (childMatch) return childMatch;
			}
		}
		return undefined;
	}

	private flattenItems(items: NavigationItem[]): NavigationItem[] {
		const flattened: NavigationItem[] = [];
		for (const item of items) {
			flattened.push(item);
			if (item.children) {
				flattened.push(...this.flattenItems(item.children));
			}
		}
		return flattened;
	}

	private addChildItem(parentId: string, item: NavigationItem): void {
		this._items = this.addChildItemRecursive(this._items, parentId, item);
	}

	private addChildItemRecursive(
		items: NavigationItem[],
		parentId: string,
		newItem: NavigationItem
	): NavigationItem[] {
		return items.map((item) => {
			if (item.id === parentId) {
				return {
					...item,
					children: [...(item.children || []), newItem]
				};
			}
			if (item.children) {
				return {
					...item,
					children: this.addChildItemRecursive(item.children, parentId, newItem)
				};
			}
			return item;
		});
	}

	private removeItemRecursive(items: NavigationItem[], id: string): NavigationItem[] {
		return items
			.filter((item) => item.id !== id)
			.map((item) => ({
				...item,
				children: item.children ? this.removeItemRecursive(item.children, id) : undefined
			}));
	}

	private updateItemRecursive(
		items: NavigationItem[],
		id: string,
		updates: Partial<NavigationItem>
	): NavigationItem[] {
		return items.map((item) => {
			if (item.id === id) {
				return { ...item, ...updates };
			}
			if (item.children) {
				return {
					...item,
					children: this.updateItemRecursive(item.children, id, updates)
				};
			}
			return item;
		});
	}

	private findItemRecursive(items: NavigationItem[], id: string): NavigationItem | undefined {
		for (const item of items) {
			if (item.id === id) {
				return item;
			}
			if (item.children) {
				const found = this.findItemRecursive(item.children, id);
				if (found) return found;
			}
		}
		return undefined;
	}

	private updateBreadcrumbsFromRoute(_route: string): void {
		const activeItem = this.activeItem;
		if (activeItem) {
			// Build breadcrumbs from navigation hierarchy
			const breadcrumbs = this.buildBreadcrumbsForItem(activeItem);
			this._breadcrumbs = breadcrumbs;
		}
	}

	private buildBreadcrumbsForItem(item: NavigationItem): BreadcrumbItem[] {
		// This is a simplified version - you might want to build full hierarchy
		return [
			{ title: 'Home', url: '/' },
			{ title: item.title, url: item.url }
		];
	}

	// Convenience methods for common operations
	isItemActive(item: NavigationItem): boolean {
		return this._currentRoute === item.url || this._currentRoute.startsWith(item.url + '/');
	}

	getItemBadge(item: NavigationItem): string | number | undefined {
		return item.badge;
	}

	hasPermission(item: NavigationItem, userPermissions?: string[]): boolean {
		if (!item.permissions || !userPermissions) return true;
		return item.permissions.some((permission) => userPermissions.includes(permission));
	}
}

// Context management
const NAVIGATION_CONTEXT_KEY = Symbol('navigation');

export function setNavigationStore(initialState?: Partial<NavigationState>): NavigationStore {
	const store = new NavigationStore(initialState);
	setContext(NAVIGATION_CONTEXT_KEY, store);
	return store;
}

export function getNavigationStore(): NavigationStore {
	const store = getContext<NavigationStore>(NAVIGATION_CONTEXT_KEY);
	if (!store) {
		throw new Error(
			'Navigation store not found. Make sure to call setNavigationStore() in a parent component.'
		);
	}
	return store;
}

// Export the store class for direct usage
export { NavigationStore };

// Helper function to create navigation store without context
export function createNavigationStore(initialState?: Partial<NavigationState>): NavigationStore {
	return new NavigationStore(initialState);
}
