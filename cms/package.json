{"name": "cms", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@dnd-kit-svelte/core": "^0.0.8", "@dnd-kit-svelte/modifiers": "^0.0.8", "@dnd-kit-svelte/sortable": "^0.0.8", "@dnd-kit-svelte/utilities": "^0.0.8", "@eslint/compat": "^1.3.1", "@eslint/js": "^9.31.0", "@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.515.0", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.22.5", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@tabler/icons-svelte": "^3.34.0", "@tailwindcss/vite": "^4.1.11", "@tanstack/table-core": "^8.21.3", "@types/d3-scale": "^4.0.9", "@types/d3-shape": "^3.1.7", "bits-ui": "^2.8.11", "clsx": "^2.1.1", "d3-scale": "^4.0.2", "d3-shape": "^3.2.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.10.1", "globals": "^16.3.0", "layerchart": "^2.0.0-next.27", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "svelte": "^5.35.6", "svelte-check": "^4.2.2", "svelte-sonner": "^1.0.5", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "vaul-svelte": "^1.0.0-next.7", "vite": "^7.0.4", "zod": "^4.0.5"}}